"""
OpenRouter model discovery and management for Geany Copilot Python plugin.

This module provides functionality to discover, cache, and manage available
free models from OpenRouter's API.
"""

import json
import logging
import requests
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class OpenRouterModel:
    """Represents an OpenRouter model."""
    id: str
    name: str
    description: str
    context_length: int
    pricing_input: float
    pricing_output: float
    is_free: bool
    
    @classmethod
    def from_api_response(cls, data: Dict[str, Any]) -> 'OpenRouterModel':
        """Create model instance from API response data."""
        pricing = data.get('pricing', {})
        input_price = float(pricing.get('prompt', '0'))
        output_price = float(pricing.get('completion', '0'))
        
        return cls(
            id=data.get('id', ''),
            name=data.get('name', ''),
            description=data.get('description', ''),
            context_length=data.get('context_length', 0),
            pricing_input=input_price,
            pricing_output=output_price,
            is_free=(input_price == 0.0 and output_price == 0.0)
        )


class OpenRouterModelManager:
    """
    Manages OpenRouter model discovery and caching.
    
    Provides functionality to fetch available models from OpenRouter's API,
    cache them locally, and filter for free models.
    """
    
    def __init__(self, config_manager):
        """
        Initialize the model manager.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        
        # Set default headers
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'Geany-Copilot-Python/1.0.0'
        })
        
        # Cache for models
        self._models_cache: Dict[str, OpenRouterModel] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = timedelta(seconds=3600)  # 1 hour default
    
    def _get_cache_ttl(self) -> timedelta:
        """Get cache TTL from configuration."""
        ttl_seconds = self.config_manager.get("api.openrouter.models_cache_ttl", 3600)
        return timedelta(seconds=ttl_seconds)
    
    def _is_cache_valid(self) -> bool:
        """Check if the models cache is still valid."""
        if not self._cache_timestamp or not self._models_cache:
            return False
        
        return datetime.now() - self._cache_timestamp < self._get_cache_ttl()
    
    def _fetch_models_from_api(self) -> List[OpenRouterModel]:
        """
        Fetch models from OpenRouter API.
        
        Returns:
            List of OpenRouterModel instances
        """
        try:
            # Get API configuration
            config = self.config_manager.get_api_config("openrouter")
            if not config or not config.get('api_key'):
                self.logger.warning("No OpenRouter API key configured")
                return []
            
            # Prepare request
            url = "https://openrouter.ai/api/v1/models"
            headers = {
                'Authorization': f'Bearer {config["api_key"]}',
                'Content-Type': 'application/json'
            }
            
            self.logger.debug(f"Fetching models from {url}")
            
            # Make request with timeout
            timeout = self.config_manager.get("performance.timeouts.default", 30)
            response = self.session.get(url, headers=headers, timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                models = []
                
                if 'data' in data:
                    for model_data in data['data']:
                        try:
                            model = OpenRouterModel.from_api_response(model_data)
                            models.append(model)
                        except Exception as e:
                            self.logger.warning(f"Error parsing model data: {e}")
                            continue
                
                self.logger.info(f"Successfully fetched {len(models)} models from OpenRouter")
                return models
            else:
                error_msg = f"Failed to fetch models: HTTP {response.status_code}"
                try:
                    error_data = response.json()
                    if 'error' in error_data:
                        error_msg += f": {error_data['error'].get('message', 'Unknown error')}"
                except:
                    error_msg += f": {response.text}"
                
                self.logger.error(error_msg)
                return []
                
        except requests.exceptions.Timeout as e:
            self.logger.error(f"Timeout fetching OpenRouter models: {e}")
            return []
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"Connection error fetching OpenRouter models: {e}")
            return []
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request error fetching OpenRouter models: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error fetching OpenRouter models: {e}")
            return []
    
    def get_available_models(self, force_refresh: bool = False) -> List[OpenRouterModel]:
        """
        Get available models, using cache if valid.
        
        Args:
            force_refresh: Force refresh from API even if cache is valid
            
        Returns:
            List of available OpenRouterModel instances
        """
        if not force_refresh and self._is_cache_valid():
            self.logger.debug("Using cached OpenRouter models")
            return list(self._models_cache.values())
        
        # Fetch fresh models from API
        models = self._fetch_models_from_api()
        
        # Update cache
        self._models_cache = {model.id: model for model in models}
        self._cache_timestamp = datetime.now()
        
        return models
    
    def get_free_models(self, force_refresh: bool = False) -> List[OpenRouterModel]:
        """
        Get only free models (pricing = $0).
        
        Args:
            force_refresh: Force refresh from API even if cache is valid
            
        Returns:
            List of free OpenRouterModel instances
        """
        all_models = self.get_available_models(force_refresh)
        free_models = [model for model in all_models if model.is_free]
        
        self.logger.info(f"Found {len(free_models)} free models out of {len(all_models)} total")
        return free_models
    
    def get_model_by_id(self, model_id: str, force_refresh: bool = False) -> Optional[OpenRouterModel]:
        """
        Get a specific model by ID.
        
        Args:
            model_id: Model ID to search for
            force_refresh: Force refresh from API even if cache is valid
            
        Returns:
            OpenRouterModel instance if found, None otherwise
        """
        models = self.get_available_models(force_refresh)
        return self._models_cache.get(model_id)
    
    def get_recommended_free_models(self, force_refresh: bool = False) -> List[OpenRouterModel]:
        """
        Get recommended free models for code assistance.
        
        Args:
            force_refresh: Force refresh from API even if cache is valid
            
        Returns:
            List of recommended free OpenRouterModel instances
        """
        free_models = self.get_free_models(force_refresh)
        
        # Sort by context length (descending) and prefer certain model families
        preferred_families = ['llama', 'gemma', 'qwen', 'deepseek']
        
        def model_score(model: OpenRouterModel) -> tuple:
            # Higher score = better recommendation
            family_score = 0
            model_id_lower = model.id.lower()
            
            for i, family in enumerate(preferred_families):
                if family in model_id_lower:
                    family_score = len(preferred_families) - i
                    break
            
            return (family_score, model.context_length, model.name)
        
        recommended = sorted(free_models, key=model_score, reverse=True)
        
        # Return top 10 recommendations
        return recommended[:10]
    
    def cleanup(self):
        """Cleanup resources."""
        if self.session:
            self.session.close()
